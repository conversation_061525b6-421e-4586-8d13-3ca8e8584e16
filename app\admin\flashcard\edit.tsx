import { Edit, FileInput, FileField, ReferenceInput, required, SimpleForm, TextInput } from "react-admin";

export const FlashcardEdit = () => {
    return (
        <Edit>
            <SimpleForm>
                <TextInput source="id" disabled />
                <TextInput 
                    source="term" 
                    validate={[required()]} 
                    label="Term"
                />
                <TextInput 
                    source="definition" 
                    validate={[required()]} 
                    multiline
                    label="Definition"
                />
                <ReferenceInput
                    source="lessonId"
                    reference="lessons"
                />
                <TextInput
                    source="imageSrc"
                    label="Image URL"
                />
                <TextInput
                    source="audioSrc"
                    label="Audio URL"
                />
                <FileInput source="image" label="Upload New Image">
                    <FileField source="src" title="title" />
                </FileInput>
                <FileInput source="audio" label="Upload New Audio">
                    <FileField source="src" title="title" />
                </FileInput>
            </SimpleForm>
        </Edit>
    );
};