import { NextResponse } from "next/server";
import { writeFile } from "fs/promises";
import { join } from "path";
import { getIsAdmin } from "@/lib/admin";

export async function POST(req: Request) {
    if (!getIsAdmin()) {
        return new NextResponse("Unauthorized", { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string;
    
    if (!file) {
        return NextResponse.json({ error: "No file uploaded" }, { status: 400 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Determine directory based on file type
    const directory = type === "audio" ? "audio" : "images";
    const uploadDir = join(process.cwd(), "public", directory);
    
    // Create a unique filename
    const uniqueFilename = `${Date.now()}-${file.name}`;
    const filePath = join(uploadDir, uniqueFilename);
    
    try {
        await writeFile(filePath, buffer);
        return NextResponse.json({ 
            url: `/${directory}/${uniqueFilename}`,
            success: true 
        });
    } catch (error) {
        console.error("Error saving file:", error);
        return NextResponse.json({ error: "Failed to save file" }, { status: 500 });
    }
}