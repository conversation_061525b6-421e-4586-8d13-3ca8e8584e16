import { <PERSON>oleanField, Datagrid, List, TextField } from "react-admin";

export const UserList = () => {
    return (
        <List>
            <Datagrid rowClick="edit">
                <TextField source="id" />
                <TextField source="userName" />
                <TextField source="userImageSrc" />
                <BooleanField source="isLocked" />
                <TextField source="points" />
                <TextField source="streak" />
            </Datagrid>
        </List>
    );
};