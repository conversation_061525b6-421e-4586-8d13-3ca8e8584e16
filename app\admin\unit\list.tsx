import { Datagrid, List, <PERSON><PERSON>ield, Reference<PERSON>ield, TextField } from "react-admin";

export const UnitList = () =>{
    return (
        <List>
            <Datagrid rowClick = "edit">
                <TextField source = "id"/>
                <TextField source = "title"/>
                <TextField source = "description"/>
                <ReferenceField source = "courseId" reference="courses"/>
                <NumberField source = "order"/>
            </Datagrid>
        </List>
    )
}