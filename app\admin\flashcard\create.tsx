import { Create, <PERSON>Field, FileInput, ReferenceInput, required, SimpleForm, TextInput } from "react-admin";

export const FlashcardCreate = () => {
    return (
        <Create>
            <SimpleForm>
                <TextInput 
                    source="term" 
                    validate={[required()]} 
                    label="Term"
                />
                <TextInput 
                    source="definition" 
                    validate={[required()]} 
                    multiline
                    label="Definition"
                />
                <ReferenceInput
                    source="lessonId"
                    reference="lessons"
                />
                <FileInput source="image" label="Image">
                    <FileField source="src" title="title" />
                </FileInput>
                <FileInput source="audio" label="Audio">
                    <FileField source="src" title="title" />
                </FileInput>
            </SimpleForm>
        </Create>
    );
};