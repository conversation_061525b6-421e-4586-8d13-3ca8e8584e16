import db from '@/db/drizzle';
import { userProgress } from '@/db/schema';
import { getIsAdmin } from '@/lib/admin';
import { eq } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export const GET = async (
    req: Request,
    {params}:{params: {userId:string}},
) => {
    if (!getIsAdmin()){
        return new NextResponse("Unauthorized", {status: 401});
    }
    const data = await db.query.userProgress.findFirst({
        where: eq(userProgress.userId, params.userId),
    });
    return NextResponse.json(data);
};

export const PUT = async (
    req: Request,
    {params}:{params: {userId:string}},
) => {
    if (!getIsAdmin()){
        return new NextResponse("Unauthorized", {status: 401});
    }

    const body = await req.json();
    const data = await db.update(userProgress).set({
        ...body,
    }).where(eq(userProgress.userId, params.userId)).returning();

    return NextResponse.json(data[0]);
};