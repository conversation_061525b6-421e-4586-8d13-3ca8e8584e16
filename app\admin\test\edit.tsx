import { Edit, NumberInput, ReferenceInput, required, SimpleForm, TextInput } from "react-admin";

export const TestEdit = () => {
    return (
        <Edit>
            <SimpleForm>
                <TextInput source="id" disabled />
                <TextInput 
                    source="title" 
                    validate={[required()]} 
                    label="Title"
                />
                <TextInput 
                    source="description" 
                    multiline
                    label="Description"
                />
                <ReferenceInput
                    source="lessonId"
                    reference="lessons"
                    validate={[required()]}
                />
                <NumberInput
                    source="timeLimit"
                    label="Time Limit (minutes)"
                    min={1}
                />
                <NumberInput
                    source="passingScore"
                    label="Passing Score (%)"
                    min={0}
                    max={100}
                />
            </SimpleForm>
        </Edit>
    );
};