<svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><filter id="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="12.9404" width="13.1154" x="16.7278" y="9.01135"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-.25"/><feGaussianBlur stdDeviation=".375"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix type="matrix" values="0 0 0 0 0.945098 0 0 0 0 0.698039 0 0 0 0 0.380392 0 0 0 1 0"/><feBlend in2="shape" mode="normal" result="effect1_innerShadow_18_599"/></filter><filter id="b" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="10.6401" width="10.6402" x="16.8654" y="11.5215"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_18_599" stdDeviation=".1"/></filter><filter id="c" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="10.2402" width="10.2403" x="4.56675" y="11.7215"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_18_599" stdDeviation=".05"/></filter><filter id="d" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="12.7584" width="5.27866" x="3.93689" y="15.255"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx=".15"/><feGaussianBlur stdDeviation=".25"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix type="matrix" values="0 0 0 0 0.054902 0 0 0 0 0.462745 0 0 0 0 0.701961 0 0 0 1 0"/><feBlend in2="shape" mode="normal" result="effect1_innerShadow_18_599"/></filter><filter id="e" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="4.82469" width="2.50579" x="13.8711" y="19.8706"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_18_599" stdDeviation=".3"/></filter><filter id="f" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="11.1624" width="4.11396" x="23.3629" y="16.6708"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx=".3" dy=".3"/><feGaussianBlur stdDeviation=".425"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix type="matrix" values="0 0 0 0 0.0705882 0 0 0 0 0.329412 0 0 0 0 0.6 0 0 0 1 0"/><feBlend in2="shape" mode="normal" result="effect1_innerShadow_18_599"/></filter><filter id="g" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="8.80629" width="3.63588" x="23.7979" y="18.5634"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_18_599" stdDeviation=".325"/></filter><filter id="h" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="4.42163" width="4.16907" x="16.0135" y="16.092"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_18_599" stdDeviation=".375"/></filter><filter id="i" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="5.81262" width="4.96881" x="18.9056" y="14.4509"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_18_599" stdDeviation=".375"/></filter><radialGradient id="j" cx="0" cy="0" gradientTransform="matrix(-4.87500411 -5.31249629 3.85553831 -3.53802884 12.4994 13.3572)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#ffe94c"/><stop offset="1" stop-color="#ffe94c" stop-opacity="0"/></radialGradient><radialGradient id="k" cx="0" cy="0" gradientTransform="matrix(6.49998786 11.78330979 -11.89495055 6.56157188 5.68692 9.01135)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".683811" stop-color="#b9706d" stop-opacity="0"/><stop offset="1" stop-color="#b9706d"/></radialGradient><radialGradient id="l" cx="0" cy="0" gradientTransform="matrix(-9.16262344 .50007932 -.50085482 -9.17683229 11.0619 15.8572)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".838992" stop-color="#f3c893" stop-opacity="0"/><stop offset="1" stop-color="#f3c893"/></radialGradient><radialGradient id="m" cx="0" cy="0" gradientTransform="matrix(5.02785648 -5.54798799 4.62598471 4.19229227 25.3239 16.5425)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#ffe76d"/><stop offset="1" stop-color="#ffe76d" stop-opacity="0"/></radialGradient><radialGradient id="n" cx="0" cy="0" gradientTransform="matrix(2.68119828 1.41306142 -1.41525274 2.68535618 17.642 11.1053)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#d6be6f"/><stop offset="1" stop-color="#d6be6f" stop-opacity="0"/></radialGradient><linearGradient id="o" gradientUnits="userSpaceOnUse" x1="-.892257" x2="5.7644" y1="5.10582" y2="6.11061"><stop offset="0" stop-color="#ecb43e"/><stop offset="1" stop-color="#925960"/></linearGradient><linearGradient id="p" gradientUnits="userSpaceOnUse" x1=".183315" x2="2.51907" y1="2.04849" y2="12.8163"><stop offset="0" stop-color="#572943"/><stop offset="1" stop-color="#4d2c30"/></linearGradient><linearGradient id="q" gradientUnits="userSpaceOnUse" x1="11.8119" x2="13.1869" y1="21.9822" y2="27.1697"><stop offset="0" stop-color="#c9136c"/><stop offset="1" stop-color="#c9136c" stop-opacity="0"/></linearGradient><linearGradient id="r" gradientUnits="userSpaceOnUse" x1="21.9994" x2="16.9994" y1="15.9196" y2="21.7572"><stop offset=".252724" stop-color="#1da2cd"/><stop offset="1" stop-color="#255eb1"/></linearGradient><linearGradient id="s" gradientUnits="userSpaceOnUse" x1="23.6244" x2="22.5931" y1="15.1384" y2="16.0134"><stop offset="0" stop-color="#0d8cba"/><stop offset="1" stop-color="#0d8cba" stop-opacity="0"/></linearGradient><radialGradient id="t" cx="0" cy="0" gradientTransform="matrix(-2.78122189 4.87501249 -3.15862995 -1.80201605 20.2494 15.4821)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".835044" stop-color="#3165cf" stop-opacity="0"/><stop offset="1" stop-color="#3165cf"/></radialGradient><radialGradient id="u" cx="0" cy="0" gradientTransform="matrix(-6.27433432 -1.29741082 1.2693873 -6.13881135 21.792 18.6617)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".807628" stop-color="#2471ad" stop-opacity="0"/><stop offset=".950123" stop-color="#2471ad"/></radialGradient><radialGradient id="v" cx="0" cy="0" gradientTransform="matrix(-.99999447 1.15625895 -1.15625895 -.99999447 16.4994 28.7009)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#43c7f4"/><stop offset="1" stop-color="#2d64cf"/></radialGradient><radialGradient id="w" cx="0" cy="0" gradientTransform="matrix(-.98322467 .823796 -.823796 -.98322467 19.2727 24.5886)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#ff48a3"/><stop offset="1" stop-color="#db1d93"/></radialGradient><radialGradient id="x" cx="0" cy="0" gradientTransform="matrix(-1.17186569 .83594539 -.83594539 -1.17186569 23.4291 27.6775)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#f7c851"/><stop offset="1" stop-color="#d97654"/></radialGradient><radialGradient id="y" cx="0" cy="0" gradientTransform="matrix(-.14062526 -1.17187261 1.17187261 -.14062526 12.101 21.2478)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#d48d3b"/><stop offset="1" stop-color="#b46518"/></radialGradient><radialGradient id="z" cx="0" cy="0" gradientTransform="matrix(-.65624483 .57813031 -.57813031 -.65624483 9.26501 26.7947)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#f8ac4b"/><stop offset="1" stop-color="#e5a437"/></radialGradient><radialGradient id="A" cx="0" cy="0" gradientTransform="matrix(.89062472 -.42968792 .42968792 .89062472 8.74158 27.0212)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".756841" stop-color="#eb9530" stop-opacity="0"/><stop offset="1" stop-color="#eb9530"/></radialGradient><linearGradient id="B" gradientUnits="userSpaceOnUse" x1="6.27556" x2="12.9322" y1="15.5392" y2="16.544"><stop offset="0" stop-color="#eabe3f"/><stop offset="1" stop-color="#976061"/></linearGradient><linearGradient id="C" gradientUnits="userSpaceOnUse" x1="9.68689" x2="9.68689" y1="10.4334" y2="23.2497"><stop offset="0" stop-color="#5e2b40"/><stop offset="1" stop-color="#6e4546"/></linearGradient><linearGradient id="D" gradientUnits="userSpaceOnUse" x1="4.23826" x2="6.04218" y1="28.2742" y2="26.6127"><stop offset="0" stop-color="#3067d9"/><stop offset="1" stop-color="#3067d9" stop-opacity="0"/></linearGradient><radialGradient id="E" cx="0" cy="0" gradientTransform="matrix(5.27331654 -.54085265 1.15712906 11.28201532 4.22082 19.985)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".655172" stop-color="#47cefa" stop-opacity="0"/><stop offset="1" stop-color="#47cefa"/></radialGradient><radialGradient id="F" cx="0" cy="0" gradientTransform="matrix(1.44788907 .42724652 -.90542209 3.06837083 5.97097 17.1658)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#1569a5"/><stop offset="1" stop-color="#1569a5" stop-opacity="0"/></radialGradient><linearGradient id="G"><stop offset="0" stop-color="#a80963"/><stop offset="1" stop-color="#a80963" stop-opacity="0"/></linearGradient><radialGradient id="H" cx="0" cy="0" gradientTransform="matrix(2.9477293 6.02808512 -1.51918263 .74287921 7.89758 14.6071)" gradientUnits="userSpaceOnUse" r="1" xlink:href="#G"/><radialGradient id="I" cx="0" cy="0" gradientTransform="matrix(-.30909577 6.36156525 -2.65138216 -.12882537 16.084 17.3011)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".198253" stop-color="#a80963"/><stop offset="1" stop-color="#a80963" stop-opacity="0"/></radialGradient><radialGradient id="J" cx="0" cy="0" gradientTransform="matrix(.39855901 -3.73193793 1.61572202 .17255393 12.3883 22.4461)" gradientUnits="userSpaceOnUse" r="1" xlink:href="#G"/><linearGradient id="K" gradientUnits="userSpaceOnUse" x1="26.687" x2="25.2698" y1="17.1697" y2="19.2322"><stop offset="0" stop-color="#184e8c"/><stop offset="1" stop-color="#184e8c" stop-opacity="0"/></linearGradient><radialGradient id="L" cx="0" cy="0" gradientTransform="matrix(-1.48987794 .40624322 -1.33533054 -4.89726209 27.1768 24.826)" gradientUnits="userSpaceOnUse" r="1"><stop offset="0" stop-color="#4bdcff"/><stop offset="1" stop-color="#4bdcff" stop-opacity="0"/></radialGradient><linearGradient id="M" gradientUnits="userSpaceOnUse" x1="26.4838" x2="25.6159" y1="26.4197" y2="19.5134"><stop offset="0" stop-color="#4bdaff"/><stop offset="1" stop-color="#4bdaff" stop-opacity="0"/></linearGradient><radialGradient id="N" cx="0" cy="0" gradientTransform="matrix(.25000045 1.01562309 -3.33142512 .82004613 18.9213 19.8884)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".306182" stop-color="#1e58a8"/><stop offset="1" stop-color="#1e58a8" stop-opacity="0"/></radialGradient><radialGradient id="O" cx="0" cy="0" gradientTransform="matrix(-2.35315698 .7187587 -1.01125395 -3.31076241 20.14 20.3259)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".590262" stop-color="#1e58a8" stop-opacity="0"/><stop offset="1" stop-color="#1e58a8"/></radialGradient><radialGradient id="P" cx="0" cy="0" gradientTransform="matrix(-.07031283 -1.53124651 1.25877362 -.05780123 19.015 18.7868)" gradientUnits="userSpaceOnUse" r="1"><stop offset=".186923" stop-color="#3258a2"/><stop offset="1" stop-color="#3258a2" stop-opacity="0"/></radialGradient><linearGradient id="Q"><stop offset="0" stop-color="#46b4e2"/><stop offset="1" stop-color="#46b4e2" stop-opacity="0"/></linearGradient><linearGradient id="R" gradientUnits="userSpaceOnUse" x1="17.8427" x2="18.0368" xlink:href="#Q" y1="16.7818" y2="19.5134"/><linearGradient id="S" gradientUnits="userSpaceOnUse" x1="22.6869" x2="20.3469" xlink:href="#Q" y1="15.4509" y2="19.8904"/><path d="m14.617 12.3669.0019-.002c-.0046-.0077-.0092-.0154-.0138-.0231-.0405-.0837-.0889-.1613-.1454-.2324-1.2207-1.8655-3.3286-3.09805-5.72447-3.09805-3.77538 0-6.83594 3.06055-6.83594 6.83595 0 2.4286 1.28874 4.5498 3.19727 5.7624 1.20828.8094 4.03396-.4305 6.51224-2.9088 2.3634-2.3634 3.6006-5.0428 3.0082-6.334z" fill="#f6bf3f"/><path d="m14.617 12.3669.0019-.002c-.0046-.0077-.0092-.0154-.0138-.0231-.0405-.0837-.0889-.1613-.1454-.2324-1.2207-1.8655-3.3286-3.09805-5.72447-3.09805-3.77538 0-6.83594 3.06055-6.83594 6.83595 0 2.4286 1.28874 4.5498 3.19727 5.7624 1.20828.8094 4.03396-.4305 6.51224-2.9088 2.3634-2.3634 3.6006-5.0428 3.0082-6.334z" fill="url(#j)"/><path d="m14.617 12.3669.0019-.002c-.0046-.0077-.0092-.0154-.0138-.0231-.0405-.0837-.0889-.1613-.1454-.2324-1.2207-1.8655-3.3286-3.09805-5.72447-3.09805-3.77538 0-6.83594 3.06055-6.83594 6.83595 0 2.4286 1.28874 4.5498 3.19727 5.7624 1.20828.8094 4.03396-.4305 6.51224-2.9088 2.3634-2.3634 3.6006-5.0428 3.0082-6.334z" fill="url(#k)"/><path d="m14.617 12.3669.0019-.002c-.0046-.0077-.0092-.0154-.0138-.0231-.0405-.0837-.0889-.1613-.1454-.2324-1.2207-1.8655-3.3286-3.09805-5.72447-3.09805-3.77538 0-6.83594 3.06055-6.83594 6.83595 0 2.4286 1.28874 4.5498 3.19727 5.7624 1.20828.8094 4.03396-.4305 6.51224-2.9088 2.3634-2.3634 3.6006-5.0428 3.0082-6.334z" fill="url(#l)"/><g filter="url(#a)"><path d="m17.1255 12.3669-.0019-.002c.0045-.0077.0091-.0154.0138-.0231.0405-.0837.0889-.1613.1454-.2324 1.2207-1.8655 3.3286-3.09805 5.7245-3.09805 3.7753 0 6.8359 3.06055 6.8359 6.83595 0 2.4286-1.2477 4.6567-3.1562 5.8693-1.2083.8093-4.0843-.5374-6.5625-3.0157-2.3635-2.3634-3.5914-5.0428-2.999-6.334z" fill="#f7cc36"/><path d="m17.1255 12.3669-.0019-.002c.0045-.0077.0091-.0154.0138-.0231.0405-.0837.0889-.1613.1454-.2324 1.2207-1.8655 3.3286-3.09805 5.7245-3.09805 3.7753 0 6.8359 3.06055 6.8359 6.83595 0 2.4286-1.2477 4.6567-3.1562 5.8693-1.2083.8093-4.0843-.5374-6.5625-3.0157-2.3635-2.3634-3.5914-5.0428-2.999-6.334z" fill="url(#m)"/><path d="m17.1255 12.3669-.0019-.002c.0045-.0077.0091-.0154.0138-.0231.0405-.0837.0889-.1613.1454-.2324 1.2207-1.8655 3.3286-3.09805 5.7245-3.09805 3.7753 0 6.8359 3.06055 6.8359 6.83595 0 2.4286-1.2477 4.6567-3.1562 5.8693-1.2083.8093-4.0843-.5374-6.5625-3.0157-2.3635-2.3634-3.5914-5.0428-2.999-6.334z" fill="url(#n)"/></g><g filter="url(#b)"><ellipse rx="2.51907" ry="6.40816" stroke="url(#o)" stroke-width=".5" transform="matrix(-.707107 .707107 .707107 .707107 22.1855 16.8416)"/></g><ellipse fill="url(#p)" rx="2.51907" ry="6.40816" transform="matrix(-.707107 .707107 .707107 .707107 22.1855 16.8416)"/><g clip-rule="evenodd" fill-rule="evenodd"><path d="m10.8847 22.8636c.5142-.2015 1.0944.0521 1.2958.5663.4327 1.1046 1.2069 3.8824-.7927 6.3668-.3463.4302-.9758.4983-1.40601.152-.43024-.3463-.49829-.9758-.152-1.406 1.30041-1.6156.84751-3.4669.48851-4.3832-.2014-.5143.0521-1.0944.5664-1.2959z" fill="#fc28a2"/><path d="m10.8847 22.8636c.5142-.2015 1.0944.0521 1.2958.5663.4327 1.1046 1.2069 3.8824-.7927 6.3668-.3463.4302-.9758.4983-1.40601.152-.43024-.3463-.49829-.9758-.152-1.406 1.30041-1.6156.84751-3.4669.48851-4.3832-.2014-.5143.0521-1.0944.5664-1.2959z" fill="url(#q)"/><path d="m22.8543 14.5457c.4568.196.6682.7252.4722 1.182-.7964 1.8558-1.9759 3.2313-3.1663 3.9955-.0145.0585-.0298.1158-.0456.1716-.1728.6094-.4498 1.1777-.7226 1.5216-.3088.3894-.8749.4548-1.2644.1459-.356-.2824-.4411-.7797-.2171-1.1605-.0898-.0076-.179-.0203-.2676-.0385-.7619-.1561-1.3253-.6901-1.5541-1.4721-.1894-.647-.1292-1.2773.1912-1.7936.3109-.5007.8057-.7956 1.3042-.9089.906-.2061 2.0541.1626 2.5077 1.266.5701-.5881 1.1355-1.4 1.5804-2.4369.1961-.4567.7253-.6681 1.182-.4721zm-5.0449 3.5012c.3384-.3812.8033.0915.6907.4963-.4773.348-.9773-.1353-.6907-.4963z" fill="url(#r)"/><path d="m22.8543 14.5457c.4568.196.6682.7252.4722 1.182-.7964 1.8558-1.9759 3.2313-3.1663 3.9955-.0145.0585-.0298.1158-.0456.1716-.1728.6094-.4498 1.1777-.7226 1.5216-.3088.3894-.8749.4548-1.2644.1459-.356-.2824-.4411-.7797-.2171-1.1605-.0898-.0076-.179-.0203-.2676-.0385-.7619-.1561-1.3253-.6901-1.5541-1.4721-.1894-.647-.1292-1.2773.1912-1.7936.3109-.5007.8057-.7956 1.3042-.9089.906-.2061 2.0541.1626 2.5077 1.266.5701-.5881 1.1355-1.4 1.5804-2.4369.1961-.4567.7253-.6681 1.182-.4721zm-5.0449 3.5012c.3384-.3812.8033.0915.6907.4963-.4773.348-.9773-.1353-.6907-.4963z" fill="url(#s)"/><path d="m22.8543 14.5457c.4568.196.6682.7252.4722 1.182-.7964 1.8558-1.9759 3.2313-3.1663 3.9955-.0145.0585-.0298.1158-.0456.1716-.1728.6094-.4498 1.1777-.7226 1.5216-.3088.3894-.8749.4548-1.2644.1459-.356-.2824-.4411-.7797-.2171-1.1605-.0898-.0076-.179-.0203-.2676-.0385-.7619-.1561-1.3253-.6901-1.5541-1.4721-.1894-.647-.1292-1.2773.1912-1.7936.3109-.5007.8057-.7956 1.3042-.9089.906-.2061 2.0541.1626 2.5077 1.266.5701-.5881 1.1355-1.4 1.5804-2.4369.1961-.4567.7253-.6681 1.182-.4721zm-5.0449 3.5012c.3384-.3812.8033.0915.6907.4963-.4773.348-.9773-.1353-.6907-.4963z" fill="url(#t)"/><path d="m22.8543 14.5457c.4568.196.6682.7252.4722 1.182-.7964 1.8558-1.9759 3.2313-3.1663 3.9955-.0145.0585-.0298.1158-.0456.1716-.1728.6094-.4498 1.1777-.7226 1.5216-.3088.3894-.8749.4548-1.2644.1459-.356-.2824-.4411-.7797-.2171-1.1605-.0898-.0076-.179-.0203-.2676-.0385-.7619-.1561-1.3253-.6901-1.5541-1.4721-.1894-.647-.1292-1.2773.1912-1.7936.3109-.5007.8057-.7956 1.3042-.9089.906-.2061 2.0541.1626 2.5077 1.266.5701-.5881 1.1355-1.4 1.5804-2.4369.1961-.4567.7253-.6681 1.182-.4721zm-5.0449 3.5012c.3384-.3812.8033.0915.6907.4963-.4773.348-.9773-.1353-.6907-.4963z" fill="url(#u)"/></g><circle cx="15.9525" cy="28.9509" fill="url(#v)" r=".90625"/><circle cx="18.9369" cy="24.9197" fill="url(#w)" r=".90625"/><circle cx="22.8745" cy="27.9197" fill="url(#x)" r=".90625"/><circle cx="11.9603" cy="20.9381" fill="url(#y)" r=".816406"/><circle cx="8.94757" cy="26.8991" fill="url(#z)" r=".739315"/><circle cx="8.94757" cy="26.8991" fill="url(#A)" r=".739315"/><g filter="url(#c)"><ellipse cx="9.68689" cy="16.8416" rx="2.51907" ry="6.40816" stroke="url(#B)" stroke-width=".3" transform="matrix(.70710678 .70710678 -.70710678 .70710678 14.746034 -1.916875)"/></g><ellipse cx="9.68689" cy="16.8416" fill="url(#C)" rx="2.51907" ry="6.40816" transform="matrix(.70710678 .70710678 -.70710678 .70710678 14.746034 -1.916875)"/><g filter="url(#d)"><path d="m6.28112 16.9651c.40539-.5672.8866-1.1454 1.433-1.7101.17418.3553.34372.7482.49923 1.1638.43162 1.1535.79654 2.5912.84799 4.0916.05945 1.7341-.51468 3.3048-1.22766 4.5405-.71105 1.2325-1.59769 2.1978-2.26776 2.7399-.42935.3474-1.05902.2809-1.40641-.1484-.34738-.4294-.28093-1.059.14842-1.4064.47576-.385 1.20281-1.1609 1.7934-2.1845.58865-1.0203 1.00411-2.2205.96118-3.4726-.04231-1.234-.34665-2.4552-.72233-3.4592-.01961-.0524-.03931-.104-.05906-.1546z" fill="#24a4dc"/><path d="m6.28112 16.9651c.40539-.5672.8866-1.1454 1.433-1.7101.17418.3553.34372.7482.49923 1.1638.43162 1.1535.79654 2.5912.84799 4.0916.05945 1.7341-.51468 3.3048-1.22766 4.5405-.71105 1.2325-1.59769 2.1978-2.26776 2.7399-.42935.3474-1.05902.2809-1.40641-.1484-.34738-.4294-.28093-1.059.14842-1.4064.47576-.385 1.20281-1.1609 1.7934-2.1845.58865-1.0203 1.00411-2.2205.96118-3.4726-.04231-1.234-.34665-2.4552-.72233-3.4592-.01961-.0524-.03931-.104-.05906-.1546z" fill="url(#D)"/><path d="m6.28112 16.9651c.40539-.5672.8866-1.1454 1.433-1.7101.17418.3553.34372.7482.49923 1.1638.43162 1.1535.79654 2.5912.84799 4.0916.05945 1.7341-.51468 3.3048-1.22766 4.5405-.71105 1.2325-1.59769 2.1978-2.26776 2.7399-.42935.3474-1.05902.2809-1.40641-.1484-.34738-.4294-.28093-1.059.14842-1.4064.47576-.385 1.20281-1.1609 1.7934-2.1845.58865-1.0203 1.00411-2.2205.96118-3.4726-.04231-1.234-.34665-2.4552-.72233-3.4592-.01961-.0524-.03931-.104-.05906-.1546z" fill="url(#E)"/><path d="m6.28112 16.9651c.40539-.5672.8866-1.1454 1.433-1.7101.17418.3553.34372.7482.49923 1.1638.43162 1.1535.79654 2.5912.84799 4.0916.05945 1.7341-.51468 3.3048-1.22766 4.5405-.71105 1.2325-1.59769 2.1978-2.26776 2.7399-.42935.3474-1.05902.2809-1.40641-.1484-.34738-.4294-.28093-1.059.14842-1.4064.47576-.385 1.20281-1.1609 1.7934-2.1845.58865-1.0203 1.00411-2.2205.96118-3.4726-.04231-1.234-.34665-2.4552-.72233-3.4592-.01961-.0524-.03931-.104-.05906-.1546z" fill="url(#F)"/></g><path d="m7.89758 15.0684c.0027-.0027.0054-.0054.00811-.0081.5136-.5136 1.04084-.9737 1.56299-1.372.14527.339.35091.7809.59412 1.2354.3363.6285.711 1.2178 1.0628 1.5998.2233.2424.4179.2751 1.0342.2693l.0803-.001c.584-.0076 1.5726-.0205 2.4043.8273.842.8274.8139 1.7792.7968 2.3618-.0022.0745-.0043.143-.0043.2044v2.1379c0 .1339.0537.2621.1491.3561l.631.6219c.3933.3876.398 1.0208.0103 1.4142-.3877.3933-1.0208.3979-1.4142.0103-.6905-.6129-1.3899-1.3738-1.3708-2.5967l-.0054-1.9437c0-.0945.0016-.1859.0029-.2666l.0001-.0081c.0015-.0857.0026-.1585.0024-.2281-.0004-.1399-.0066-.2346-.0191-.3094-.0182-.1091-.0498-.2002-.1839-.3307l-.0099-.0096-.0096-.0099c-.2051-.211-.3827-.2365-1.0409-.2302l-.0283.0003c-.6173.0061-1.6388.0162-2.49609-.9146-.52323-.5681-.99223-1.3329-1.35515-2.0112-.14708-.2748-.28219-.5459-.40178-.7988z" fill="#fa3f99"/><path d="m7.89758 15.0684c.0027-.0027.0054-.0054.00811-.0081.5136-.5136 1.04084-.9737 1.56299-1.372.14527.339.35091.7809.59412 1.2354.3363.6285.711 1.2178 1.0628 1.5998.2233.2424.4179.2751 1.0342.2693l.0803-.001c.584-.0076 1.5726-.0205 2.4043.8273.842.8274.8139 1.7792.7968 2.3618-.0022.0745-.0043.143-.0043.2044v2.1379c0 .1339.0537.2621.1491.3561l.631.6219c.3933.3876.398 1.0208.0103 1.4142-.3877.3933-1.0208.3979-1.4142.0103-.6905-.6129-1.3899-1.3738-1.3708-2.5967l-.0054-1.9437c0-.0945.0016-.1859.0029-.2666l.0001-.0081c.0015-.0857.0026-.1585.0024-.2281-.0004-.1399-.0066-.2346-.0191-.3094-.0182-.1091-.0498-.2002-.1839-.3307l-.0099-.0096-.0096-.0099c-.2051-.211-.3827-.2365-1.0409-.2302l-.0283.0003c-.6173.0061-1.6388.0162-2.49609-.9146-.52323-.5681-.99223-1.3329-1.35515-2.0112-.14708-.2748-.28219-.5459-.40178-.7988z" fill="url(#H)"/><path d="m7.89758 15.0684c.0027-.0027.0054-.0054.00811-.0081.5136-.5136 1.04084-.9737 1.56299-1.372.14527.339.35091.7809.59412 1.2354.3363.6285.711 1.2178 1.0628 1.5998.2233.2424.4179.2751 1.0342.2693l.0803-.001c.584-.0076 1.5726-.0205 2.4043.8273.842.8274.8139 1.7792.7968 2.3618-.0022.0745-.0043.143-.0043.2044v2.1379c0 .1339.0537.2621.1491.3561l.631.6219c.3933.3876.398 1.0208.0103 1.4142-.3877.3933-1.0208.3979-1.4142.0103-.6905-.6129-1.3899-1.3738-1.3708-2.5967l-.0054-1.9437c0-.0945.0016-.1859.0029-.2666l.0001-.0081c.0015-.0857.0026-.1585.0024-.2281-.0004-.1399-.0066-.2346-.0191-.3094-.0182-.1091-.0498-.2002-.1839-.3307l-.0099-.0096-.0096-.0099c-.2051-.211-.3827-.2365-1.0409-.2302l-.0283.0003c-.6173.0061-1.6388.0162-2.49609-.9146-.52323-.5681-.99223-1.3329-1.35515-2.0112-.14708-.2748-.28219-.5459-.40178-.7988z" fill="url(#I)"/><path d="m7.89758 15.0684c.0027-.0027.0054-.0054.00811-.0081.5136-.5136 1.04084-.9737 1.56299-1.372.14527.339.35091.7809.59412 1.2354.3363.6285.711 1.2178 1.0628 1.5998.2233.2424.4179.2751 1.0342.2693l.0803-.001c.584-.0076 1.5726-.0205 2.4043.8273.842.8274.8139 1.7792.7968 2.3618-.0022.0745-.0043.143-.0043.2044v2.1379c0 .1339.0537.2621.1491.3561l.631.6219c.3933.3876.398 1.0208.0103 1.4142-.3877.3933-1.0208.3979-1.4142.0103-.6905-.6129-1.3899-1.3738-1.3708-2.5967l-.0054-1.9437c0-.0945.0016-.1859.0029-.2666l.0001-.0081c.0015-.0857.0026-.1585.0024-.2281-.0004-.1399-.0066-.2346-.0191-.3094-.0182-.1091-.0498-.2002-.1839-.3307l-.0099-.0096-.0096-.0099c-.2051-.211-.3827-.2365-1.0409-.2302l-.0283.0003c-.6173.0061-1.6388.0162-2.49609-.9146-.52323-.5681-.99223-1.3329-1.35515-2.0112-.14708-.2748-.28219-.5459-.40178-.7988z" fill="url(#J)"/><g filter="url(#e)"><path d="m14.671 20.6707v1.6784c0 .3877.1501.7603.4188 1.0397l.487.5065" stroke="#ff5cac" stroke-linecap="round" stroke-width=".4"/></g><g filter="url(#f)"><path d="m25.3761 16.6708c.4809.6487.8754 1.2554 1.1468 1.8466-.4377.7088-.8376 1.5927-1.0311 2.5059-.3245 1.5314-.106 3.2304 1.4089 4.82.381.3998.3658 1.0328-.034 1.4138s-1.0328.3658-1.4138-.034c-2.035-2.1354-2.3582-4.5354-1.9176-6.6144.3303-1.5589 1.0852-2.9366 1.8408-3.9379z" fill="#2bbafe"/><path d="m25.3761 16.6708c.4809.6487.8754 1.2554 1.1468 1.8466-.4377.7088-.8376 1.5927-1.0311 2.5059-.3245 1.5314-.106 3.2304 1.4089 4.82.381.3998.3658 1.0328-.034 1.4138s-1.0328.3658-1.4138-.034c-2.035-2.1354-2.3582-4.5354-1.9176-6.6144.3303-1.5589 1.0852-2.9366 1.8408-3.9379z" fill="url(#K)"/></g><path d="m25.3761 16.6708c.4809.6487.8754 1.2554 1.1468 1.8466-.4377.7088-.8376 1.5927-1.0311 2.5059-.3245 1.5314-.106 3.2304 1.4089 4.82.381.3998.3658 1.0328-.034 1.4138s-1.0328.3658-1.4138-.034c-2.035-2.1354-2.3582-4.5354-1.9176-6.6144.3303-1.5589 1.0852-2.9366 1.8408-3.9379z" fill="url(#L)"/><g filter="url(#g)"><path d="m25.4213 19.5134c-.6927 1.1459-1.45 4.1313 1.0625 6.9063" stroke="url(#M)" stroke-linecap="round" stroke-width=".6"/></g><path d="m20.257 19.6597c-.0323.0216-.0645.0428-.0968.0635-.0145.0586-.0298.1158-.0456.1716-.1728.6095-.4498 1.1777-.7226 1.5216-.3088.3895-.8749.4548-1.2644.1459-.356-.2823-.4411-.7797-.2171-1.1605-.0096-.0008-.0191-.0017-.0286-.0026.5072-.0182 1.6658-.2959 2.3011-.6983.0253-.016.05-.0297.074-.0412z" fill="#2a82d3"/><path d="m20.257 19.6597c-.0323.0216-.0645.0428-.0968.0635-.0145.0586-.0298.1158-.0456.1716-.1728.6095-.4498 1.1777-.7226 1.5216-.3088.3895-.8749.4548-1.2644.1459-.356-.2823-.4411-.7797-.2171-1.1605-.0096-.0008-.0191-.0017-.0286-.0026.5072-.0182 1.6658-.2959 2.3011-.6983.0253-.016.05-.0297.074-.0412z" fill="url(#N)"/><path d="m20.257 19.6597c-.0323.0216-.0645.0428-.0968.0635-.0145.0586-.0298.1158-.0456.1716-.1728.6095-.4498 1.1777-.7226 1.5216-.3088.3895-.8749.4548-1.2644.1459-.356-.2823-.4411-.7797-.2171-1.1605-.0096-.0008-.0191-.0017-.0286-.0026.5072-.0182 1.6658-.2959 2.3011-.6983.0253-.016.05-.0297.074-.0412z" fill="url(#O)"/><path d="m20.0919 17.4547c-.4536-1.1034-1.6017-1.4721-2.5077-1.266-.4985.1133-.9933.4082-1.3042.9089-.2259.3641-.3225.7849-.2934 1.2283h1.7427c-.0141-.0907.0084-.1887.0801-.279.3384-.3812.8033.0915.6907.4963.3552-.1607 1.1751-.6587 1.5918-1.0885z" fill="url(#P)"/><g filter="url(#h)"><path d="m18.8424 19.483c-.4467.0851-1.3105.0507-1.7189-.8001-.5148-1.0724.8894-2.2626 2.0592-1.1354" stroke="url(#R)" stroke-linecap="round" stroke-width=".5"/></g><g filter="url(#i)"><path d="m22.8744 15.4509c-.2917.6875-1.2438 2.7375-2.9688 3.8125" stroke="url(#S)" stroke-linecap="round" stroke-width=".5"/></g></svg>