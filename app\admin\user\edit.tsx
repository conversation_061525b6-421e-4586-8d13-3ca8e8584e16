import { BooleanInput, Edit, NumberInput, SimpleForm, TextInput } from "react-admin";

export const UserEdit = () => {
    return (
        <Edit>
            <SimpleForm>
                <TextInput source="id" disabled />
                <TextInput source="userName" />
                <TextInput source="userImageSrc" />
                <BooleanInput 
                    source="isLocked" 
                    label="Lock User Account"
                    helperText="Locked users cannot access their accounts"
                />
                <NumberInput source="points" disabled />
                <NumberInput source="streak" disabled />
            </SimpleForm>
        </Edit>
    );
};