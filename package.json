{"name": "lingo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:studio": "npx drizzle-kit studio", "db:push": "npx drizzle-kit push:pg", "db:seed": "tsx ./scripts/seed.ts", "db:reset": "tsx ./scripts/reset.ts"}, "dependencies": {"@clerk/nextjs": "^6.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.1.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.40.1", "lucide-react": "^0.475.0", "next": "15.3.3", "next-themes": "^0.4.6", "openai": "^5.3.0", "ra-data-simple-rest": "^5.8.3", "react": "^18.3.1", "react-admin": "^5.8.3", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-confetti": "^6.4.0", "react-dom": "^18.3.1", "react-use": "^17.6.0", "shadcn-ui": "^0.9.5", "sonner": "^2.0.3", "stripe": "^18.0.0", "swr": "^2.3.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/pg": "^8.11.11", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.30.6", "eslint": "^9", "eslint-config-next": "15.3.3", "pg": "^8.14.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5"}}