"use client";

import simpleRest<PERSON>rovider from "ra-data-simple-rest";
import { Admin, Resource } from "react-admin";
import { ChallengeCreate } from "./challenge/create";
import { ChallengeEdit } from "./challenge/edit";
import { ChallengeList } from "./challenge/list";
import { ChallengeOptionCreate } from "./challengeOption/create";
import { ChallengeOptionEdit } from "./challengeOption/edit";
import { ChallengeOptionList } from "./challengeOption/list";
import { CourseCreate } from "./course/create";
import { CourseEdit } from "./course/edit";
import { CourseList } from "./course/list";
import { LessonCreate } from "./lesson/create";
import { LessonEdit } from "./lesson/edit";
import { LessonList } from "./lesson/list";
import { UnitCreate } from "./unit/create";
import { UnitEdit } from "./unit/edit";
import { UnitList } from "./unit/list";
import { UserList } from "./user/list";
import { UserEdit } from "./user/edit";
import { FlashcardList } from "./flashcard/list";
import { FlashcardCreate } from "./flashcard/create";
import { FlashcardEdit } from "./flashcard/edit";
import { TestList } from "./test/list";
import { TestCreate } from "./test/create";
import { TestEdit } from "./test/edit";
import { Dashboard } from "./dashboard";

const dataProvider = simpleRestProvider("/api");

const App = () => {
  return (
    <Admin dataProvider={dataProvider} dashboard={Dashboard}>
      <Resource
        name="courses"
        list={CourseList}
        create={CourseCreate}
        edit={CourseEdit}
        recordRepresentation="title"
      />
      <Resource
        name="units"
        list={UnitList}
        create={UnitCreate}
        edit={UnitEdit}
        recordRepresentation="title"
      />
      <Resource
        name="lessons"
        list={LessonList}
        create={LessonCreate}
        edit={LessonEdit}
        recordRepresentation="title"
      />
      <Resource
        name="challenges"
        list={ChallengeList}
        create={ChallengeCreate}
        edit={ChallengeEdit}
        recordRepresentation="question"
      />
      <Resource
        name="challengeOptions"
        list={ChallengeOptionList}
        create={ChallengeOptionCreate}
        edit={ChallengeOptionEdit}
        recordRepresentation="text"
        options={{ label: "Challenge Options" }}
      />
      <Resource
        name="users"
        list={UserList}
        edit={UserEdit}
        recordRepresentation="userName"
        options={{ label: "Users" }}
      />
      <Resource
        name="flashcards"
        list={FlashcardList}
        create={FlashcardCreate}
        edit={FlashcardEdit}
        recordRepresentation="term"
      />
      <Resource
        name="tests"
        list={TestList}
        create={TestCreate}
        edit={TestEdit}
        recordRepresentation="title"
      />
    </Admin>
  );

};

export default App;
