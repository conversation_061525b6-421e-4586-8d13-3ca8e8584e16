import React from 'react';
import { Title } from 'react-admin';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement } from 'chart.js';
import { Pie, Bar } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);

export const Dashboard = () => {
    // In a real app, you would fetch this data from your API
    const userStats = {
        active: 120,
        locked: 5,
        total: 125
    };

    const courseCompletionData = {
        labels: ['Course A', 'Course B', 'Course C', 'Course D'],
        datasets: [
            {
                label: 'Completion Rate (%)',
                data: [78, 65, 91, 42],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.6)',
                    'rgba(54, 162, 235, 0.6)',
                    'rgba(153, 102, 255, 0.6)',
                    'rgba(255, 159, 64, 0.6)',
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)',
                ],
                borderWidth: 1,
            },
        ],
    };

    const userStatusData = {
        labels: ['Active', 'Locked'],
        datasets: [
            {
                label: 'User Status',
                data: [userStats.active, userStats.locked],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.6)',
                    'rgba(255, 99, 132, 0.6)',
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)',
                ],
                borderWidth: 1,
            },
        ],
    };

    return (
        <div>
            <Title title="Dashboard" />
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
                <div style={{
                    width: '45%',
                    minWidth: '300px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    padding: '16px',
                    backgroundColor: '#fff',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                    <div>
                        <h2>User Status</h2>
                        <Pie data={userStatusData} />
                        <div style={{ textAlign: 'center', marginTop: '10px' }}>
                            <p>Total Users: {userStats.total}</p>
                        </div>
                    </div>
                </div>
                <div style={{
                    width: '45%',
                    minWidth: '300px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    padding: '16px',
                    backgroundColor: '#fff',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                    <div>
                        <h2>Course Completion Rates</h2>
                        <Bar
                            data={courseCompletionData}
                            options={{
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100
                                    }
                                }
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};