import { relations } from "drizzle-orm";
import { boolean, integer, pgEnum, pgTable, serial, text, timestamp } from "drizzle-orm/pg-core";

export const courses = pgTable("courses", {
    id: serial("id").primaryKey(),
    title: text("title").notNull(),
    imageSrc: text("image_src").notNull(),
});

export const coursesRelations = relations(courses, ({ many }) => ({
    userProgress: many(userProgress),
    units: many(units),
}));

export const units = pgTable("units", {
    id: serial("id").primaryKey(),
    title: text("title").notNull(), //Unit 1
    description: text("description").notNull(), //Learn the basics of English
    courseId: integer("course_id").references(() => courses.id, { onDelete: "cascade" }).notNull(),
    order: integer("order").notNull(),
});

export const unitsRelations = relations(units, ({ many, one }) => ({
    course: one(courses, {
        fields: [units.courseId],
        references: [courses.id]
    }),
    lessons: many(lessons),
}));

export const lessons = pgTable("lessons", {
    id: serial("id").primaryKey(),
    title: text("title").notNull(),
    unitId: integer("unit_id").references(() => units.id, { onDelete: "cascade" }).notNull(),
    order: integer("order").notNull(),
});

export const lessonsRelations = relations(lessons, ({ one, many }) => ({
    unit: one(units, {
        fields: [lessons.unitId],
        references: [units.id],
    }),
    challenges: many(challenges),
    flashcards: many(flashcards),
}));

export const challengesEnum = pgEnum("type", ["SELECT", "ASSIST", "GRAMMAR", "VOCABULARY", "LISTENING", "READING"]);

export const challenges = pgTable("challenges", {
    id: serial("id").primaryKey(),
    lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "cascade" }).notNull(),
    type: challengesEnum("type").notNull(),
    question: text("question").notNull(),
    order: integer("order").notNull(),
});

export const challengesRelations = relations(challenges, ({ one, many }) => ({
    lesson: one(lessons, {
        fields: [challenges.lessonId],
        references: [lessons.id],
    }),
    challengeOptions: many(challengeOptions),
    challengeProgress: many(challengeProgress),
}));

export const challengeOptions = pgTable("challenge_options", {
    id: serial("id").primaryKey(),
    challengeId: integer("challenge_id").references(() => challenges.id, { onDelete: "cascade" }).notNull(),
    text: text("text").notNull(),
    correct: boolean("correct").notNull(),
    imageSrc: text("image_src"),
    audioSrc: text("audio_src"),
});

export const challengeOptionsRelations = relations(challengeOptions, ({ one }) => ({
    challenge: one(challenges, {
        fields: [challengeOptions.challengeId],
        references: [challenges.id],
    }),
}));

export const challengeProgress = pgTable("challenge_progress", {
    id: serial("id").primaryKey(),
    userId: text("user_id").notNull(),
    challengeId: integer("challenge_id").references(() => challenges.id, { onDelete: "cascade" }).notNull(),
    completed: boolean("completed").notNull().default(false),
});

export const challengeProgressRelations = relations(challengeProgress, ({ one }) => ({
    challenge: one(challenges, {
        fields: [challengeProgress.challengeId],
        references: [challenges.id],
    }),
}));

export const userProgress = pgTable("user_progress", {
    userId: text("user_id").primaryKey(),
    userName: text("user_name").notNull().default("User"),
    userImageSrc: text("user_image_src").notNull().default("/mascot.svg"),
    activeCourseId: integer("active_course_id").references(() => courses.id,
        { onDelete: "cascade" }),
    hearts: integer("hearts").notNull().default(5),
    points: integer("points").notNull().default(0),
});

export const userProgressRelations = relations(userProgress, ({ one }) =>
({
    activeCourse: one(courses, {
        fields: [userProgress.activeCourseId],
        references: [courses.id],
    }),
}));

export const userSubscription = pgTable("user_subscription", {
    id: serial("id").primaryKey(),
    userId: text("user_id").notNull().unique(),
    stripeCustomerId: text("stripe_customer_id").notNull().unique(),
    stripeSubscriptionId: text("stripe_subscription_id").notNull().unique(),
    stripePriceId: text("stripe_price_id").notNull(),
    stripeCurrentPeriodEnd: timestamp("stripe_current_period_end").notNull(),
});

export const tests = pgTable("tests", {
    id: serial("id").primaryKey(),
    title: text("title").notNull(),
    imageSrc: text("image_src").notNull(),
    duration: integer("duration").notNull().default(30), // Duration in minutes
    createdAt: timestamp("created_at").defaultNow(),
});

export const testsRelations = relations(tests, ({ many }) => ({
    questions: many(testQuestions),
}));

export const testQuestions = pgTable("test_questions", {
    id: serial("id").primaryKey(),
    testId: integer("test_id").references(() => tests.id, { onDelete: "cascade" }),
    text: text("text").notNull(),
    order: integer("order").notNull(),
});

export const testQuestionsRelations = relations(testQuestions, ({ one, many }) => ({
    test: one(tests, {
        fields: [testQuestions.testId],
        references: [tests.id],
    }),
    options: many(testOptions),
}));

export const testOptions = pgTable("test_options", {
    id: serial("id").primaryKey(),
    questionId: integer("question_id").references(() => testQuestions.id, { onDelete: "cascade" }),
    text: text("text").notNull(),
    isCorrect: boolean("is_correct").notNull().default(false),
});

export const testOptionsRelations = relations(testOptions, ({ one }) => ({
    question: one(testQuestions, {
        fields: [testOptions.questionId],
        references: [testQuestions.id],
    }),
}));

export const vocabularyTopics = pgTable("vocabulary_topics", {
    id: serial("id").primaryKey(),
    title: text("title").notNull(),
    description: text("description"),
    imageSrc: text("image_src").notNull(),
    createdAt: timestamp("created_at").defaultNow(),
});

export const vocabularyWords = pgTable("vocabulary_words", {
    id: serial("id").primaryKey(),
    word: text("word").notNull(),
    topicId: integer("topic_id").references(() => vocabularyTopics.id, { onDelete: "cascade" }),
    vietnameseMeaning: text("vietnamese_meaning"),
    createdAt: timestamp("created_at").defaultNow(),
});

export const flashcards = pgTable("flashcards", {
    id: serial("id").primaryKey(),
    term: text("term").notNull(),
    definition: text("definition").notNull(),
    lessonId: integer("lesson_id").references(() => lessons.id, { onDelete: "cascade" }),
    imageSrc: text("image_src"),
    audioSrc: text("audio_src"),
    createdAt: timestamp("created_at").defaultNow(),
});

export const flashcardsRelations = relations(flashcards, ({ one }) => ({
    lesson: one(lessons, {
        fields: [flashcards.lessonId],
        references: [lessons.id],
    }),
}));
